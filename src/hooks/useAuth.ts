import { useAuthInfo, useLogoutFunction, useRedirectFunctions } from '@propelauth/react'
import { useEffect, useCallback } from 'react'
import { apiService } from '../services/api'
import { clearStaleAuthData } from '../services/propelauth'
import type { User } from '../types'

export const useAuth = () => {
  const authInfo = useAuthInfo()
  const logoutFn = useLogoutFunction()
  const { redirectToLoginPage, redirectToSignupPage } = useRedirectFunctions()

  // Handle authentication errors
  const handleAuthError = useCallback((error: any) => {
    console.error('Authentication error:', error)

    if (error && (
      error.message?.includes('refresh_token') ||
      error.message?.includes('401') ||
      error.message?.includes('Unauthorized') ||
      error.status === 401
    )) {
      console.log('Token refresh failed, clearing auth data')
      clearStaleAuthData()
      // Don't automatically redirect here, let the user decide
    }
  }, [])

  // Update API service with access token when auth state changes
  useEffect(() => {
    if (authInfo.accessToken) {
      apiService.setAccessToken(authInfo.accessToken)
    } else {
      apiService.setAccessToken(null)
    }
  }, [authInfo.accessToken])

  // Enhanced auth state logging and error detection
  useEffect(() => {
    if (authInfo.loading === false) {
      if (!authInfo.user && !authInfo.accessToken) {
        console.log('Auth state: No user or token available')
      } else if (authInfo.user && authInfo.accessToken) {
        console.log('Auth state: User authenticated', {
          userId: authInfo.user.userId,
          email: authInfo.user.email,
          hasToken: !!authInfo.accessToken
        })
      }
    }
  }, [authInfo.loading, authInfo.user, authInfo.accessToken])

  // Listen for PropelAuth errors
  useEffect(() => {
    const handleError = (event: any) => {
      if (event.detail && event.detail.error) {
        handleAuthError(event.detail.error)
      }
    }

    window.addEventListener('propelauth:error', handleError)
    return () => window.removeEventListener('propelauth:error', handleError)
  }, [handleAuthError])

  const user: User | null = authInfo.user ? {
    id: authInfo.user.userId,
    email: authInfo.user.email || '',
    created_at: '', // PropelAuth doesn't provide this directly
  } : null

  const signUp = async () => {
    redirectToSignupPage()
  }

  const signIn = async () => {
    redirectToLoginPage()
  }

  const signOut = async () => {
    logoutFn(true) // true = redirect to login page after logout
  }

  return {
    user,
    loading: authInfo.loading,
    signUp,
    signIn,
    signOut,
    isLoggedIn: !!authInfo.user,
  }
}
