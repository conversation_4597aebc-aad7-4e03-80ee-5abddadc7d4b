/**
 * Debugging utilities for PropelAuth authentication issues
 */

export const debugAuthState = () => {
  console.group('🔍 PropelAuth Debug Information')
  
  // Environment variables
  console.log('Environment:', {
    PROPELAUTH_URL: import.meta.env.VITE_PROPELAUTH_URL,
    API_BASE_URL: import.meta.env.VITE_API_BASE_URL,
    DEV_MODE: import.meta.env.VITE_DEV_MODE,
  })
  
  // Local storage inspection
  console.log('LocalStorage keys:', Object.keys(localStorage))
  
  const authKeys = []
  for (let i = 0; i < localStorage.length; i++) {
    const key = localStorage.key(i)
    if (key && (key.includes('propelauth') || key.includes('auth') || key.includes('token'))) {
      authKeys.push({
        key,
        value: localStorage.getItem(key)?.substring(0, 50) + '...' // Truncate for security
      })
    }
  }
  console.log('Auth-related localStorage:', authKeys)
  
  // Session storage inspection
  console.log('SessionStorage keys:', Object.keys(sessionStorage))
  
  // Network connectivity test
  fetch(import.meta.env.VITE_PROPELAUTH_URL + '/api/v1/health', { 
    method: 'GET',
    mode: 'cors'
  })
    .then(response => {
      console.log('PropelAuth connectivity test:', {
        status: response.status,
        ok: response.ok,
        headers: Object.fromEntries(response.headers.entries())
      })
    })
    .catch(error => {
      console.error('PropelAuth connectivity test failed:', error)
    })
  
  console.groupEnd()
}

export const testPropelAuthEndpoints = async () => {
  const baseUrl = import.meta.env.VITE_PROPELAUTH_URL
  const endpoints = [
    '/api/v1/health',
    '/api/v1/refresh_token',
    '/api/v1/userinfo'
  ]
  
  console.group('🌐 PropelAuth Endpoints Test')
  
  for (const endpoint of endpoints) {
    try {
      const response = await fetch(baseUrl + endpoint, {
        method: 'GET',
        mode: 'cors',
        credentials: 'include'
      })
      
      console.log(`${endpoint}:`, {
        status: response.status,
        statusText: response.statusText,
        ok: response.ok
      })
    } catch (error) {
      console.error(`${endpoint} failed:`, error)
    }
  }
  
  console.groupEnd()
}

export const clearAllAuthData = () => {
  console.log('🧹 Clearing all authentication data...')
  
  // Clear localStorage
  const keysToRemove = []
  for (let i = 0; i < localStorage.length; i++) {
    const key = localStorage.key(i)
    if (key && (key.includes('propelauth') || key.includes('auth') || key.includes('token'))) {
      keysToRemove.push(key)
    }
  }
  
  keysToRemove.forEach(key => {
    localStorage.removeItem(key)
    console.log('Removed localStorage key:', key)
  })
  
  // Clear sessionStorage
  sessionStorage.clear()
  console.log('Cleared sessionStorage')
  
  // Clear cookies (if any)
  document.cookie.split(";").forEach(cookie => {
    const eqPos = cookie.indexOf("=")
    const name = eqPos > -1 ? cookie.substr(0, eqPos).trim() : cookie.trim()
    if (name.includes('propelauth') || name.includes('auth') || name.includes('token')) {
      document.cookie = `${name}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/`
      console.log('Cleared cookie:', name)
    }
  })
  
  console.log('✅ All authentication data cleared')
}

// Add to window for easy debugging in console
if (typeof window !== 'undefined') {
  (window as any).authDebug = {
    debugAuthState,
    testPropelAuthEndpoints,
    clearAllAuthData
  }
  
  console.log('🔧 Auth debugging tools available at window.authDebug')
}
