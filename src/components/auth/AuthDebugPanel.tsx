import { useState } from 'react'
import { debugAuthState, testPropelAuthEndpoints, clearAllAuthData } from '../../utils/authDebug'

interface AuthDebugPanelProps {
  show?: boolean
}

export const AuthDebugPanel = ({ show = false }: AuthDebugPanelProps) => {
  const [isVisible, setIsVisible] = useState(show)
  const [isRunningTests, setIsRunningTests] = useState(false)

  if (!import.meta.env.DEV && !isVisible) {
    return null
  }

  const handleDebugAuth = () => {
    debugAuthState()
  }

  const handleTestEndpoints = async () => {
    setIsRunningTests(true)
    try {
      await testPropelAuthEndpoints()
    } finally {
      setIsRunningTests(false)
    }
  }

  const handleClearAuth = () => {
    if (confirm('Are you sure you want to clear all authentication data? This will log you out.')) {
      clearAllAuthData()
      window.location.reload()
    }
  }

  if (!isVisible) {
    return (
      <div style={{
        position: 'fixed',
        bottom: '20px',
        right: '20px',
        zIndex: 9999
      }}>
        <button
          onClick={() => setIsVisible(true)}
          style={{
            padding: '8px 12px',
            backgroundColor: '#007bff',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            fontSize: '12px',
            cursor: 'pointer'
          }}
        >
          🔧 Auth Debug
        </button>
      </div>
    )
  }

  return (
    <div style={{
      position: 'fixed',
      bottom: '20px',
      right: '20px',
      width: '300px',
      backgroundColor: 'white',
      border: '1px solid #ddd',
      borderRadius: '8px',
      padding: '16px',
      boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
      zIndex: 9999,
      fontSize: '14px'
    }}>
      <div style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: '12px'
      }}>
        <h4 style={{ margin: 0, fontSize: '16px' }}>🔧 Auth Debug Panel</h4>
        <button
          onClick={() => setIsVisible(false)}
          style={{
            background: 'none',
            border: 'none',
            fontSize: '18px',
            cursor: 'pointer',
            padding: '0',
            color: '#666'
          }}
        >
          ×
        </button>
      </div>

      <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
        <button
          onClick={handleDebugAuth}
          style={{
            padding: '8px 12px',
            backgroundColor: '#28a745',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer',
            fontSize: '12px'
          }}
        >
          🔍 Debug Auth State
        </button>

        <button
          onClick={handleTestEndpoints}
          disabled={isRunningTests}
          style={{
            padding: '8px 12px',
            backgroundColor: isRunningTests ? '#6c757d' : '#17a2b8',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: isRunningTests ? 'not-allowed' : 'pointer',
            fontSize: '12px'
          }}
        >
          {isRunningTests ? '⏳ Testing...' : '🌐 Test Endpoints'}
        </button>

        <button
          onClick={handleClearAuth}
          style={{
            padding: '8px 12px',
            backgroundColor: '#dc3545',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer',
            fontSize: '12px'
          }}
        >
          🧹 Clear Auth Data
        </button>
      </div>

      <div style={{
        marginTop: '12px',
        padding: '8px',
        backgroundColor: '#f8f9fa',
        borderRadius: '4px',
        fontSize: '11px',
        color: '#666'
      }}>
        <strong>Quick Tips:</strong>
        <ul style={{ margin: '4px 0', paddingLeft: '16px' }}>
          <li>Check browser console for detailed logs</li>
          <li>Clear auth data if you see 401 errors</li>
          <li>Test endpoints to check connectivity</li>
        </ul>
      </div>
    </div>
  )
}
