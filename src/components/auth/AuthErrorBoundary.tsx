import React, { Component, ReactNode } from 'react'
import { clearStaleAuthData } from '../../services/propelauth'

interface Props {
  children: ReactNode
}

interface State {
  hasError: boolean
  error?: Error
  errorType?: 'auth' | 'network' | 'unknown'
}

export class AuthErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props)
    this.state = { hasError: false }
  }

  static getDerivedStateFromError(error: Error): State {
    // Determine error type
    let errorType: 'auth' | 'network' | 'unknown' = 'unknown'

    if (error.message.includes('refresh_token') ||
        error.message.includes('401') ||
        error.message.includes('Unauthorized') ||
        error.message.includes('authentication') ||
        error.message.includes('propelauth')) {
      errorType = 'auth'
    } else if (error.message.includes('fetch') ||
               error.message.includes('network') ||
               error.message.includes('Failed to fetch')) {
      errorType = 'network'
    }

    return { hasError: true, error, errorType }
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('Auth Error:', error, errorInfo)

    // Check if it's a PropelAuth related error
    if (error.message.includes('refresh_token') ||
        error.message.includes('401') ||
        error.message.includes('Unauthorized') ||
        error.message.includes('propelauth')) {
      console.log('PropelAuth authentication error detected - clearing storage')
      clearStaleAuthData()
    }
  }

  handleRetry = () => {
    // Clear storage and reload
    clearStaleAuthData()
    window.location.reload()
  }

  handleClearAndRedirect = () => {
    // Clear all auth data and redirect to login
    clearStaleAuthData()
    window.location.href = '/'
  }

  render() {
    if (this.state.hasError) {
      const { error, errorType } = this.state

      let title = 'Application Error'
      let description = 'An unexpected error occurred.'
      let showDetails = true

      if (errorType === 'auth') {
        title = 'Authentication Error'
        description = 'Your session has expired or there was an authentication issue. Please sign in again.'
      } else if (errorType === 'network') {
        title = 'Network Error'
        description = 'Unable to connect to the server. Please check your internet connection and try again.'
      }

      return (
        <div className="auth-error-boundary">
          <div className="container">
            <div className="auth-container">
              <h2>{title}</h2>
              <p>{description}</p>

              {showDetails && error && (
                <div className="auth-error-details" style={{
                  marginTop: '1rem',
                  padding: '1rem',
                  backgroundColor: '#f8f9fa',
                  borderRadius: '4px',
                  fontSize: '0.875rem',
                  color: '#666'
                }}>
                  <p><strong>Technical Details:</strong></p>
                  <p>{error.message}</p>
                </div>
              )}

              <div style={{ marginTop: '1.5rem', display: 'flex', gap: '0.5rem', flexDirection: 'column' }}>
                {errorType === 'auth' ? (
                  <button
                    onClick={this.handleClearAndRedirect}
                    className="btn btn-primary btn-full"
                  >
                    Clear Session & Sign In
                  </button>
                ) : (
                  <button
                    onClick={this.handleRetry}
                    className="btn btn-primary btn-full"
                  >
                    Retry
                  </button>
                )}

                <button
                  onClick={() => window.location.reload()}
                  className="btn btn-outline btn-full"
                >
                  Reload Page
                </button>
              </div>
            </div>
          </div>
        </div>
      )
    }

    return this.props.children
  }
}
