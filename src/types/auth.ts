import { ReactNode } from 'react'
import type { User } from './index'

export interface AuthState {
  user: User | null
  loading: boolean
  isLoggedIn: boolean
  accessToken: string | null
}

export interface AuthActions {
  signIn: () => Promise<void> | void
  signUp: () => Promise<void> | void
  signOut: () => Promise<void> | void
}

export interface AuthProvider {
  // Provider component that wraps the app
  Provider: React.ComponentType<{ children: ReactNode }>
  
  // Hook to get auth state and actions
  useAuth: () => AuthState & AuthActions
  
  // Method to set access token in API service
  setApiToken: (token: string | null) => void
}

export type AuthProviderType = 'clerk' | 'propelauth'
