import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom'
import { DashboardPage } from './pages/DashboardPage'
import { AuthErrorBoundary } from './components/auth/AuthErrorBoundary'
import { authProvider, getAuthProviderType } from './services/auth'

// Import debugging utilities in development
if (import.meta.env.DEV) {
  import('./utils/authDebug').then(({ debugAuthState }) => {
    // Run debug info on app start in development
    setTimeout(() => {
      console.log('🚀 App started in development mode')
      console.log('🔐 Auth provider:', getAuthProviderType())
      if (getAuthProviderType() === 'propelauth') {
        debugAuthState()
      }
    }, 1000)
  })
}

function App() {
  const AuthProviderComponent = authProvider.Provider

  return (
    <AuthErrorBoundary>
      <AuthProviderComponent>
        <Router>
          <Routes>
            <Route path="/dashboard" element={<DashboardPage />} />
            <Route path="/" element={<Navigate to="/dashboard" replace />} />
            <Route path="*" element={<Navigate to="/dashboard" replace />} />
          </Routes>
        </Router>
      </AuthProviderComponent>
    </AuthErrorBoundary>
  )
}

export default App
