import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom'
import { AuthProvider } from '@propelauth/react'
import { DashboardPage } from './pages/DashboardPage'
import { authUrl } from './services/propelauth'
import { AuthErrorBoundary } from './components/auth/AuthErrorBoundary'

// Import debugging utilities in development
if (import.meta.env.DEV) {
  import('./utils/authDebug').then(({ debugAuthState }) => {
    // Run debug info on app start in development
    setTimeout(() => {
      console.log('🚀 App started in development mode')
      debugAuthState()
    }, 1000)
  })
}

function App() {
  return (
    <AuthErrorBoundary>
      <AuthProvider authUrl={authUrl}>
        <Router>
          <Routes>
            <Route path="/dashboard" element={<DashboardPage />} />
            <Route path="/" element={<Navigate to="/dashboard" replace />} />
            <Route path="*" element={<Navigate to="/dashboard" replace />} />
          </Routes>
        </Router>
      </AuthProvider>
    </AuthErrorBoundary>
  )
}

export default App
