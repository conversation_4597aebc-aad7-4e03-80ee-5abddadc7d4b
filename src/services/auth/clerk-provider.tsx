import React, { useEffect } from 'react'
import { <PERSON><PERSON><PERSON><PERSON>, useAuth as useClerkAuth, useUser, useClerk, SignIn, SignUp } from '@clerk/clerk-react'
import { apiService } from '../api'
import type { AuthProvider, AuthState, AuthActions } from '../../types/auth'
import type { User } from '../../types'

const clerkPublishableKey = import.meta.env.VITE_CLERK_PUBLISHABLE_KEY

if (!clerkPublishableKey) {
  throw new Error('Missing VITE_CLERK_PUBLISHABLE_KEY environment variable')
}

const ClerkWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  return (
    <ClerkProvider publishableKey={clerkPublishableKey}>
      {children}
    </ClerkProvider>
  )
}

const useClerkAuthState = (): AuthState & AuthActions => {
  const { isSignedIn, isLoaded, getToken, signOut: clerkSignOut } = useClerkAuth()
  const { user: clerkUser } = useUser()
  const clerk = useClerk()

  // Update API service with access token when auth state changes
  useEffect(() => {
    const updateToken = async () => {
      if (isSignedIn && isLoaded) {
        try {
          const token = await getToken()
          apiService.setAccessToken(token)
        } catch (error) {
          console.error('Error getting Clerk token:', error)
          apiService.setAccessToken(null)
        }
      } else {
        apiService.setAccessToken(null)
      }
    }

    updateToken()
  }, [isSignedIn, isLoaded, getToken])

  // Enhanced auth state logging
  useEffect(() => {
    if (isLoaded) {
      if (!isSignedIn) {
        console.log('Auth state: No user signed in')
      } else if (clerkUser) {
        console.log('Auth state: User authenticated', {
          userId: clerkUser.id,
          email: clerkUser.primaryEmailAddress?.emailAddress,
          hasToken: true
        })
      }
    }
  }, [isLoaded, isSignedIn, clerkUser])

  const user: User | null = clerkUser ? {
    id: clerkUser.id,
    email: clerkUser.primaryEmailAddress?.emailAddress || '',
    created_at: clerkUser.createdAt?.toISOString() || '',
  } : null

  const signUp = async () => {
    // Open Clerk's sign up modal
    clerk.openSignUp()
  }

  const signIn = async () => {
    // Open Clerk's sign in modal
    clerk.openSignIn()
  }

  const signOut = async () => {
    try {
      await clerkSignOut()
    } catch (error) {
      console.error('Error signing out:', error)
    }
  }

  return {
    user,
    loading: !isLoaded,
    isLoggedIn: !!isSignedIn && !!clerkUser,
    accessToken: null, // Clerk manages tokens internally
    signIn,
    signUp,
    signOut,
  }
}

export const clerkAuthProvider: AuthProvider = {
  Provider: ClerkWrapper,
  useAuth: useClerkAuthState,
  setApiToken: (token: string | null) => {
    apiService.setAccessToken(token)
  }
}

// Export Clerk components for use in UI
export { SignIn as ClerkSignIn, SignUp as ClerkSignUp }
