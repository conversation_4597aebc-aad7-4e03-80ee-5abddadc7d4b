import type { AuthProvider, AuthProviderType } from '../../types/auth'
import { propelAuthProvider } from './propelauth-provider'
import { clerkAuthProvider } from './clerk-provider'

const getAuthProviderType = (): AuthProviderType => {
  const provider = import.meta.env.VITE_AUTH_PROVIDER
  
  if (provider === 'clerk' || provider === 'propelauth') {
    return provider
  }
  
  // Default to clerk if not specified or invalid
  console.warn(`Invalid auth provider "${provider}", defaulting to clerk`)
  return 'clerk'
}

const createAuthProvider = (): AuthProvider => {
  const providerType = getAuthProviderType()
  
  console.log(`Initializing auth provider: ${providerType}`)
  
  switch (providerType) {
    case 'clerk':
      return clerkAuthProvider
    case 'propelauth':
      return propelAuthProvider
    default:
      throw new Error(`Unsupported auth provider: ${providerType}`)
  }
}

export const authProvider = createAuthProvider()
export { getAuthProviderType }
export type { AuthProvider, AuthProviderType }
