import { AuthProvider } from '@propelauth/react'

const propelAuthUrl = import.meta.env.VITE_PROPELAUTH_URL

if (!propelAuthUrl) {
  throw new Error('Missing VITE_PROPELAUTH_URL environment variable')
}

// Ensure the URL doesn't have a trailing slash
const cleanAuthUrl = propelAuthUrl.replace(/\/$/, '')

// Log the auth URL for debugging
console.log('PropelAuth URL:', cleanAuthUrl)

// Clear any stale authentication data on startup if we detect issues
const clearStaleAuthData = () => {
  try {
    // Clear PropelAuth specific storage keys
    const keysToRemove = []
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i)
      if (key && (key.includes('propelauth') || key.includes('auth') || key.includes('token'))) {
        keysToRemove.push(key)
      }
    }

    keysToRemove.forEach(key => {
      localStorage.removeItem(key)
      console.log('Cleared stale auth data:', key)
    })

    // Also clear session storage
    sessionStorage.clear()
  } catch (error) {
    console.warn('Error clearing stale auth data:', error)
  }
}

// Listen for authentication errors globally
window.addEventListener('unhandledrejection', (event) => {
  if (event.reason && typeof event.reason === 'object') {
    const error = event.reason
    if (error.message && error.message.includes('refresh_token') ||
        (error.status === 401 && error.message.includes('Unauthorized'))) {
      console.warn('PropelAuth refresh token error detected, clearing auth data')
      clearStaleAuthData()
    }
  }
})

export { AuthProvider, clearStaleAuthData }
export const authUrl = cleanAuthUrl
