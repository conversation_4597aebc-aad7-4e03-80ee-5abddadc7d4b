import { Layout } from '../components/layout/Layout'
import { LoginForm } from '../components/auth/LoginForm'
import { AuthDebugPanel } from '../components/auth/AuthDebugPanel'

export const LoginPage = () => {
  return (
    <Layout showHeader={false}>
      <div className="auth-page">
        <div className="container">
          <div className="auth-container">
            <LoginForm />
          </div>
        </div>
      </div>

      {/* Show debug panel in development */}
      {import.meta.env.DEV && <AuthDebugPanel />}
    </Layout>
  )
}
