import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react()],
  root: 'src',
  envDir: '../', // Look for .env files in the project root
  build: {
    outDir: '../dist',
    emptyOutDir: true,
  },
  publicDir: '../public',
  server: {
    port: 5173,
    host: true, // Allow external connections
    cors: true, // Enable CORS for development
    strictPort: true, // Don't try other ports if 5173 is in use
  },
})
